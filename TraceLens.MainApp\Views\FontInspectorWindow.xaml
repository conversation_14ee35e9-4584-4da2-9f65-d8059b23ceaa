<Window x:Class="TraceLens.MainApp.Views.FontInspectorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="字体检查器" Height="500" Width="700">
    <Grid>
        <ScrollViewer>
            <StackPanel Margin="20">
                <TextBlock Text="字体检查器" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <!-- 测试不同的字体族名称 -->
                <GroupBox Header="测试不同字体族名称" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="测试图标: &#xf015; &#xf007; &#xf013; &#xf030;" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="1. 使用 #FontAwesome:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="2. 使用 #Font Awesome:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#Font Awesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="3. 使用 #FontAwesome Regular:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome Regular" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="4. 不使用字体族名称:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="5. 使用OTF文件 #FontAwesome:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="6. 使用OTF文件不带族名:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf" 
                                   FontSize="24" Margin="0,5,0,10"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 测试不同的Unicode字符 -->
                <GroupBox Header="测试不同Unicode字符" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock Text="使用最有效的字体路径测试更多图标:" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="基础图标:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf015; &#xf007; &#xf013; &#xf030; &#xf019; &#xf021; &#xf040; &#xf0c7;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                        
                        <TextBlock Text="更多图标:" FontWeight="Bold"/>
                        <TextBlock Text="&#xf0e7; &#xf0f3; &#xf11c; &#xf140; &#xf188; &#xf1c0; &#xf1ea; &#xf240;" 
                                   FontFamily="pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome" 
                                   FontSize="24" Margin="0,5,0,10"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 字体信息 -->
                <GroupBox Header="字体加载信息" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock x:Name="FontLoadInfo" Text="字体加载信息将在窗口加载后显示..." FontFamily="Consolas"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
