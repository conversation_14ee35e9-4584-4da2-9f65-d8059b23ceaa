using System;
using System.Collections.Generic;
using System.Linq;

namespace TraceLens.ThemeControl.Helpers
{
    /// <summary>
    /// FontAwesome图标管理器 - 支持动态加载所有FontAwesome图标
    /// </summary>
    public static class FontAwesomeManager
    {
        private static readonly Dictionary<string, string> _iconMap = new();
        
        static FontAwesomeManager()
        {
            InitializeDefaultIcons();
        }

        /// <summary>
        /// 根据图标名称获取Unicode字符
        /// </summary>
        /// <param name="iconName">图标名称（支持带fa-前缀或不带前缀）</param>
        /// <returns>Unicode字符，如果找不到返回问号</returns>
        public static string GetIconCode(string iconName)
        {
            if (string.IsNullOrEmpty(iconName))
                return "?";

            // 移除fa-前缀（如果有）
            string cleanName = iconName.StartsWith("fa-") ? iconName.Substring(3) : iconName;
            
            // 尝试获取图标
            if (_iconMap.TryGetValue(cleanName.ToLower(), out string? code))
            {
                return code;
            }

            // 如果找不到，记录日志并返回问号
            System.Diagnostics.Debug.WriteLine($"FontAwesome icon not found: {iconName}");
            return "?";
        }

        /// <summary>
        /// 添加自定义图标映射
        /// </summary>
        /// <param name="iconName">图标名称</param>
        /// <param name="unicodeCode">Unicode字符</param>
        public static void AddIcon(string iconName, string unicodeCode)
        {
            if (!string.IsNullOrEmpty(iconName) && !string.IsNullOrEmpty(unicodeCode))
            {
                _iconMap[iconName.ToLower()] = unicodeCode;
            }
        }

        /// <summary>
        /// 批量添加图标映射
        /// </summary>
        /// <param name="icons">图标字典</param>
        public static void AddIcons(Dictionary<string, string> icons)
        {
            foreach (var icon in icons)
            {
                AddIcon(icon.Key, icon.Value);
            }
        }

        /// <summary>
        /// 获取所有可用的图标名称
        /// </summary>
        /// <returns>图标名称列表</returns>
        public static IEnumerable<string> GetAvailableIcons()
        {
            return _iconMap.Keys.OrderBy(x => x);
        }

        /// <summary>
        /// 检查图标是否存在
        /// </summary>
        /// <param name="iconName">图标名称</param>
        /// <returns>是否存在</returns>
        public static bool HasIcon(string iconName)
        {
            if (string.IsNullOrEmpty(iconName))
                return false;

            string cleanName = iconName.StartsWith("fa-") ? iconName.Substring(3) : iconName;
            return _iconMap.ContainsKey(cleanName.ToLower());
        }

        /// <summary>
        /// 初始化默认的FontAwesome图标
        /// </summary>
        private static void InitializeDefaultIcons()
        {
            // 常用图标
            _iconMap["home"] = "\uf015";
            _iconMap["user"] = "\uf007";
            _iconMap["search"] = "\uf002";
            _iconMap["settings"] = "\uf013";
            _iconMap["cog"] = "\uf013";
            _iconMap["gear"] = "\uf013";
            _iconMap["menu"] = "\uf0c9";
            _iconMap["bars"] = "\uf0c9";
            _iconMap["close"] = "\uf00d";
            _iconMap["times"] = "\uf00d";
            _iconMap["x"] = "\uf00d";

            // 工业相关图标
            _iconMap["camera"] = "\uf030";
            _iconMap["industry"] = "\uf275";
            _iconMap["wrench"] = "\uf0ad";
            _iconMap["gauge"] = "\uf0e4";
            _iconMap["microchip"] = "\uf2db";
            _iconMap["cpu"] = "\uf2db";

            // 操作图标
            _iconMap["play"] = "\uf04b";
            _iconMap["pause"] = "\uf04c";
            _iconMap["stop"] = "\uf04d";
            _iconMap["record"] = "\uf03d";
            _iconMap["save"] = "\uf0c7";
            _iconMap["floppy-disk"] = "\uf0c7";
            _iconMap["load"] = "\uf07c";
            _iconMap["folder-open"] = "\uf07c";

            // 状态图标
            _iconMap["check"] = "\uf00c";
            _iconMap["success"] = "\uf00c";
            _iconMap["warning"] = "\uf071";
            _iconMap["exclamation-triangle"] = "\uf071";
            _iconMap["error"] = "\uf00d";
            _iconMap["info"] = "\uf129";
            _iconMap["info-circle"] = "\uf05a";

            // 导航图标
            _iconMap["arrow-left"] = "\uf060";
            _iconMap["arrow-right"] = "\uf061";
            _iconMap["arrow-up"] = "\uf062";
            _iconMap["arrow-down"] = "\uf063";
            _iconMap["chevron-left"] = "\uf053";
            _iconMap["chevron-right"] = "\uf054";
            _iconMap["chevron-up"] = "\uf077";
            _iconMap["chevron-down"] = "\uf078";

            // 数据图标
            _iconMap["chart"] = "\uf080";
            _iconMap["chart-bar"] = "\uf080";
            _iconMap["table"] = "\uf0ce";
            _iconMap["list"] = "\uf03a";
            _iconMap["grid"] = "\uf00a";
            _iconMap["th"] = "\uf00a";

            // 更多常用图标
            _iconMap["edit"] = "\uf044";
            _iconMap["pencil"] = "\uf040";
            _iconMap["delete"] = "\uf1f8";
            _iconMap["trash"] = "\uf1f8";
            _iconMap["plus"] = "\uf067";
            _iconMap["minus"] = "\uf068";
            _iconMap["refresh"] = "\uf021";
            _iconMap["sync"] = "\uf021";
            _iconMap["download"] = "\uf019";
            _iconMap["upload"] = "\uf093";
            _iconMap["print"] = "\uf02f";
            _iconMap["copy"] = "\uf0c5";
            _iconMap["cut"] = "\uf0c4";
            _iconMap["paste"] = "\uf0ea";
            _iconMap["undo"] = "\uf0e2";
            _iconMap["redo"] = "\uf01e";
            _iconMap["zoom-in"] = "\uf00e";
            _iconMap["zoom-out"] = "\uf010";
            _iconMap["fullscreen"] = "\uf065";
            _iconMap["minimize"] = "\uf066";
        }
    }
}
