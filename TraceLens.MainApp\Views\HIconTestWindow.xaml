<Window x:Class="TraceLens.MainApp.Views.HIconTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        Title="HIcon测试" Height="400" Width="600">
    <Grid>
        <ScrollViewer>
            <StackPanel Margin="20">
                <TextBlock Text="HIcon控件测试" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <!-- 使用默认样式的HIcon -->
                <GroupBox Header="使用默认样式的HIcon" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <controls:HIcon IconName="home" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="user" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="settings" IconSize="24" Margin="5"/>
                            <controls:HIcon IconName="camera" IconSize="24" Margin="5"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 手动设置FontFamily的HIcon -->
                <GroupBox Header="手动设置FontFamily的HIcon" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <StackPanel Orientation="Horizontal" Margin="0,5" x:Name="ManualFontPanel">
                            <!-- 这些将在代码中创建 -->
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 直接设置Unicode的HIcon -->
                <GroupBox Header="直接设置Unicode的HIcon" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <StackPanel Orientation="Horizontal" Margin="0,5">
                            <controls:HIcon IconCode="&#xf015;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf007;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf013;" IconSize="24" Margin="5"/>
                            <controls:HIcon IconCode="&#xf030;" IconSize="24" Margin="5"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 调试信息 -->
                <GroupBox Header="调试信息" Margin="0,0,0,20">
                    <StackPanel Margin="10">
                        <TextBlock x:Name="DebugInfo" Text="调试信息将在窗口加载后显示..." FontFamily="Consolas"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
