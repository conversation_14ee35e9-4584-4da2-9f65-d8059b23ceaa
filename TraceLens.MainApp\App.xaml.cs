﻿using System.Windows;
using Prism.Ioc;
using Prism.DryIoc;
using TraceLens.MainApp.Views;
using TraceLens.MainApp.ViewModels;
using TraceLens.MainApp.Services;

namespace TraceLens.MainApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : PrismApplication
    {
        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // Register services
            containerRegistry.RegisterSingleton<IInspectionService, InspectionService>();
            containerRegistry.RegisterSingleton<IConfigurationService, ConfigurationService>();

            // Register view models
            containerRegistry.Register<MainWindowViewModel>();
        }
    }
}
