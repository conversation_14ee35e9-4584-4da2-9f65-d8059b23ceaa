using System;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace TraceLens.MainApp.Views
{
    public partial class FontTestWindow : Window
    {
        public FontTestWindow()
        {
            InitializeComponent();
            Loaded += FontTestWindow_Loaded;
        }

        private void FontTestWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 显示字体信息
            var fontInfo = GetFontInfo();
            FontInfoText.Text = fontInfo;
        }

        private string GetFontInfo()
        {
            var info = new System.Text.StringBuilder();

            // 检查系统字体
            info.AppendLine("系统可用字体 (包含 'Font' 或 'Awesome' 的):");
            var fonts = Fonts.SystemFontFamilies
                .Where(f => f.Source.Contains("Font", StringComparison.OrdinalIgnoreCase) ||
                           f.Source.Contains("Awesome", StringComparison.OrdinalIgnoreCase))
                .Take(10);

            foreach (var font in fonts)
            {
                info.AppendLine($"  - {font.Source}");
            }

            info.AppendLine();

            // 测试字体路径
            string[] testPaths = {
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome"
            };

            info.AppendLine("字体路径测试:");
            foreach (string path in testPaths)
            {
                try
                {
                    var font = new FontFamily(path);
                    info.AppendLine($"  ✓ {path}");
                }
                catch (Exception ex)
                {
                    info.AppendLine($"  ✗ {path} - {ex.Message}");
                }
            }

            info.AppendLine();

            // 测试StaticResource
            try
            {
                var fontResource = this.FindResource("FontAwesome") as FontFamily;
                if (fontResource != null)
                {
                    info.AppendLine($"✓ StaticResource FontAwesome: {fontResource.Source}");
                }
                else
                {
                    info.AppendLine("✗ StaticResource FontAwesome: 未找到资源");
                }
            }
            catch (Exception ex)
            {
                info.AppendLine($"✗ StaticResource FontAwesome: {ex.Message}");
            }

            // 测试FontAwesome管理器
            info.AppendLine();
            info.AppendLine("FontAwesome管理器测试:");
            string[] testIcons = { "home", "user", "settings", "camera" };
            foreach (string iconName in testIcons)
            {
                string iconCode = TraceLens.ThemeControl.Helpers.FontAwesomeManager.GetIconCode(iconName);
                info.AppendLine($"  {iconName} -> {iconCode} (Unicode: {(iconCode.Length > 0 ? ((int)iconCode[0]).ToString("X4") : "Empty")})");
            }

            return info.ToString();
        }
    }
}
