using System.Collections.Generic;

namespace TraceLens.MainApp.Services
{
    public class ConfigurationService : IConfigurationService
    {
        private readonly Dictionary<string, string> _settings = new();

        public ConfigurationService()
        {
            // Initialize with default settings
            _settings["AppName"] = "TraceLens Smart Vision Inspector";
            _settings["Version"] = "1.0.0";
            _settings["MaxImageSize"] = "10485760"; // 10MB
        }

        public string GetSetting(string key)
        {
            return _settings.TryGetValue(key, out var value) ? value : string.Empty;
        }

        public void SetSetting(string key, string value)
        {
            _settings[key] = value;
        }

        public T GetSetting<T>(string key, T defaultValue)
        {
            if (!_settings.TryGetValue(key, out var value))
                return defaultValue;

            try
            {
                return (T)System.Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
