using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using TraceLens.ThemeControl.Helpers;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// HIcon控件 - 支持动态加载FontAwesome图标
    /// </summary>
    public class HIcon : TextBlock
    {
        static HIcon()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HIcon), new FrameworkPropertyMetadata(typeof(HIcon)));
        }

        public HIcon()
        {
            // 设置默认属性
            HorizontalAlignment = HorizontalAlignment.Center;
            VerticalAlignment = VerticalAlignment.Center;
            TextAlignment = TextAlignment.Center;

            // 直接设置FontAwesome字体 - FontAwesome 4
            bool fontSet = false;
            string[] fontPaths = {
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf"
            };

            foreach (string fontPath in fontPaths)
            {
                try
                {
                    var testFont = new FontFamily(fontPath);
                    FontFamily = testFont;
                    System.Diagnostics.Debug.WriteLine($"HIcon constructor - Font SUCCESS: {fontPath}");
                    System.Diagnostics.Debug.WriteLine($"HIcon constructor - Font Source: {testFont.Source}");
                    fontSet = true;
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"HIcon constructor - Font FAILED: {fontPath} - {ex.Message}");
                }
            }

            if (!fontSet)
            {
                // 使用系统默认字体作为后备
                FontFamily = new FontFamily("Segoe UI Symbol");
            }

            // 确保Foreground有默认值，避免UnsetValue问题
            if (ReadLocalValue(ForegroundProperty) == DependencyProperty.UnsetValue)
            {
                SetCurrentValue(ForegroundProperty, System.Windows.Media.Brushes.Black);
            }

        }

        #region IconName 依赖属性

        /// <summary>
        /// 图标名称依赖属性
        /// </summary>
        public static readonly DependencyProperty IconNameProperty =
            DependencyProperty.Register(
                nameof(IconName),
                typeof(string),
                typeof(HIcon),
                new PropertyMetadata(string.Empty, OnIconNameChanged));

        /// <summary>
        /// 图标名称（支持FontAwesome图标名称，如"home", "camera", "fa-settings"等）
        /// </summary>
        public string IconName
        {
            get => (string)GetValue(IconNameProperty);
            set => SetValue(IconNameProperty, value);
        }

        private static void OnIconNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon && e.NewValue is string iconName)
            {
                icon.UpdateIconFromName(iconName);
            }
        }

        #endregion

        #region IconCode 依赖属性

        /// <summary>
        /// 图标Unicode字符依赖属性
        /// </summary>
        public static readonly DependencyProperty IconCodeProperty =
            DependencyProperty.Register(
                nameof(IconCode),
                typeof(string),
                typeof(HIcon),
                new PropertyMetadata(string.Empty, OnIconCodeChanged));

        /// <summary>
        /// 图标Unicode字符（直接设置Unicode字符，如"\uf015"）
        /// </summary>
        public string IconCode
        {
            get => (string)GetValue(IconCodeProperty);
            set => SetValue(IconCodeProperty, value);
        }

        private static void OnIconCodeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon && e.NewValue is string iconCode)
            {
                icon.UpdateIconFromCode(iconCode);
            }
        }

        #endregion

        #region IconSize 依赖属性

        /// <summary>
        /// 图标大小依赖属性
        /// </summary>
        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(
                nameof(IconSize),
                typeof(double),
                typeof(HIcon),
                new PropertyMetadata(16.0, OnIconSizeChanged));

        /// <summary>
        /// 图标大小
        /// </summary>
        public double IconSize
        {
            get => (double)GetValue(IconSizeProperty);
            set => SetValue(IconSizeProperty, value);
        }

        private static void OnIconSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon && e.NewValue is double size)
            {
                icon.FontSize = size;
            }
        }

        #endregion

        #region IconColor 依赖属性

        /// <summary>
        /// 图标颜色依赖属性
        /// </summary>
        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register(
                nameof(IconColor),
                typeof(Brush),
                typeof(HIcon),
                new PropertyMetadata(null, OnIconColorChanged));

        /// <summary>
        /// 图标颜色
        /// </summary>
        public Brush? IconColor
        {
            get => (Brush?)GetValue(IconColorProperty);
            set => SetValue(IconColorProperty, value);
        }

        private static void OnIconColorChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                // 使用SetCurrentValue避免覆盖绑定和样式
                if (e.NewValue is Brush brush && brush != null)
                {
                    icon.SetCurrentValue(ForegroundProperty, brush);
                }
                else
                {
                    // 当IconColor为null时，恢复到默认的文本颜色
                    icon.SetCurrentValue(ForegroundProperty, System.Windows.Media.Brushes.Black);
                }
            }
        }

        #endregion

        /// <summary>
        /// 根据图标名称更新图标
        /// </summary>
        /// <param name="iconName">图标名称</param>
        private void UpdateIconFromName(string iconName)
        {
            if (string.IsNullOrEmpty(iconName))
            {
                Text = string.Empty;
                System.Diagnostics.Debug.WriteLine("HIcon UpdateIconFromName - Empty iconName");
                return;
            }

            // 使用FontAwesome管理器获取图标字符
            string iconCode = FontAwesomeManager.GetIconCode(iconName);
            Text = iconCode;

            System.Diagnostics.Debug.WriteLine($"HIcon UpdateIconFromName - IconName: '{iconName}' -> IconCode: '{iconCode}' (Unicode: {(iconCode.Length > 0 ? ((int)iconCode[0]).ToString("X4") : "Empty")})");
            System.Diagnostics.Debug.WriteLine($"HIcon UpdateIconFromName - FontFamily: {FontFamily?.Source ?? "null"}");
        }

        /// <summary>
        /// 根据Unicode字符更新图标
        /// </summary>
        /// <param name="iconCode">Unicode字符</param>
        private void UpdateIconFromCode(string iconCode)
        {
            Text = iconCode ?? string.Empty;
        }

        /// <summary>
        /// 应用模板时初始化
        /// </summary>
        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            System.Diagnostics.Debug.WriteLine($"HIcon OnApplyTemplate - IconName: '{IconName}', IconCode: '{IconCode}', IconSize: {IconSize}");

            // 确保字体族正确设置 - FontAwesome 4
            string[] fontPaths = {
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf"
            };

            bool fontSet = false;

            // 首先尝试从资源获取
            try
            {
                var resourceFont = this.FindResource("FontAwesome") as FontFamily;
                if (resourceFont != null)
                {
                    FontFamily = resourceFont;
                    fontSet = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"HIcon OnApplyTemplate - Resource font failed: {ex.Message}");
            }

            // 如果资源获取失败，尝试直接路径
            if (!fontSet)
            {
                foreach (string fontPath in fontPaths)
                {
                    try
                    {
                        var testFont = new FontFamily(fontPath);
                        FontFamily = testFont;
                        fontSet = true;
                        break;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"HIcon OnApplyTemplate - FontFamily failed: {fontPath} - {ex.Message}");
                    }
                }
            }

            if (!fontSet)
            {
                FontFamily = new FontFamily("Segoe UI Symbol");
            }

            // 应用IconSize到FontSize
            if (IconSize > 0)
            {
                FontSize = IconSize;
            }

            // 应用IconColor到Foreground
            if (IconColor != null)
            {
                Foreground = IconColor;
            }

            // 优先使用IconCode，其次使用IconName
            if (!string.IsNullOrEmpty(IconCode))
            {
                UpdateIconFromCode(IconCode);
            }
            else if (!string.IsNullOrEmpty(IconName))
            {
                UpdateIconFromName(IconName);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("HIcon OnApplyTemplate - No IconName or IconCode provided");
            }

        }

        /// <summary>
        /// 设置图标（便捷方法）
        /// </summary>
        /// <param name="iconName">图标名称</param>
        /// <param name="size">图标大小（可选）</param>
        /// <param name="color">图标颜色（可选）</param>
        public void SetIcon(string iconName, double? size = null, Brush? color = null)
        {
            IconName = iconName;
            
            if (size.HasValue)
                IconSize = size.Value;
                
            if (color != null)
                IconColor = color;
        }
    }
}
