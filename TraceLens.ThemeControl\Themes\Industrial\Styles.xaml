<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 合并颜色和字体资源 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Fonts.xaml"/>
        <ResourceDictionary Source="../../Resources/Icons.xaml"/>
    </ResourceDictionary.MergedDictionaries>
    
    <!-- 基础样式 -->
    <!-- 窗口样式 -->
    <Style x:Key="IndustrialWindow" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeMedium}"/>
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="ResizeMode" Value="CanResize"/>
    </Style>
    
    <!-- 基础文本样式 -->
    <Style x:Key="IndustrialTextBlock" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeMedium}"/>
    </Style>
    
    <!-- 图标文本样式 -->
    <Style x:Key="IconText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontFamily" Value="{StaticResource FontAwesome}"/>
        <Setter Property="FontSize" Value="{StaticResource IconSizeMedium}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    
    <!-- 标题样式 -->
    <Style x:Key="TitleText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeXLarge}"/>
        <Setter Property="FontWeight" Value="Bold"/>
    </Style>
    
    <!-- 副标题样式 -->
    <Style x:Key="SubtitleText" TargetType="TextBlock" BasedOn="{StaticResource IndustrialTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLarge}"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>
    
    <!-- 基础按钮样式 -->
    <Style x:Key="IndustrialButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeMedium}"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                            <Setter Property="BorderBrush" Value="{StaticResource BorderLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
</ResourceDictionary>
