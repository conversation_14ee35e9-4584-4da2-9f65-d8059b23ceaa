using Prism.Commands;
using Prism.Mvvm;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace TraceLens.MainApp.ViewModels
{
    public class IconDemoViewModel : BindableBase
    {
        private string _selectedIconName = "camera";
        private double _selectedIconSize = 24;

        public IconDemoViewModel()
        {
            // 初始化常用图标列表
            CommonIcons = new ObservableCollection<string>
            {
                "home", "user", "search", "settings", "menu", "close",
                "camera", "industry", "wrench", "gauge", "microchip", "cog",
                "play", "pause", "stop", "save", "load", "edit", "delete",
                "check", "warning", "times", "info",
                "arrow-left", "arrow-right", "arrow-up", "arrow-down"
            };

            // 初始化图标大小选项
            IconSizes = new ObservableCollection<double>
            {
                12, 16, 20, 24, 32, 48, 64
            };

            // 初始化命令
            ChangeIconCommand = new DelegateCommand<string>(OnChangeIcon);
            ChangeSizeCommand = new DelegateCommand<double?>(OnChangeSize);
        }

        public ObservableCollection<string> CommonIcons { get; }
        public ObservableCollection<double> IconSizes { get; }

        public string SelectedIconName
        {
            get => _selectedIconName;
            set => SetProperty(ref _selectedIconName, value);
        }

        public double SelectedIconSize
        {
            get => _selectedIconSize;
            set => SetProperty(ref _selectedIconSize, value);
        }

        public ICommand ChangeIconCommand { get; }
        public ICommand ChangeSizeCommand { get; }

        private void OnChangeIcon(string iconName)
        {
            if (!string.IsNullOrEmpty(iconName))
            {
                SelectedIconName = iconName;
            }
        }

        private void OnChangeSize(double? size)
        {
            if (size.HasValue)
            {
                SelectedIconSize = size.Value;
            }
        }
    }
}
