<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 工业风格主色调 -->
    <!-- 主要颜色 -->
    <Color x:Key="PrimaryColor">#FF2D2D30</Color>
    <Color x:Key="PrimaryLightColor">#FF3E3E42</Color>
    <Color x:Key="PrimaryDarkColor">#FF1E1E1E</Color>
    
    <!-- 强调色 -->
    <Color x:Key="AccentColor">#FF007ACC</Color>
    <Color x:Key="AccentLightColor">#FF1BA1E2</Color>
    <Color x:Key="AccentDarkColor">#FF005A9E</Color>
    
    <!-- 状态颜色 -->
    <Color x:Key="SuccessColor">#FF4CAF50</Color>
    <Color x:Key="WarningColor">#FFFF9800</Color>
    <Color x:Key="ErrorColor">#FFF44336</Color>
    <Color x:Key="InfoColor">#FF2196F3</Color>
    
    <!-- 背景颜色 -->
    <Color x:Key="BackgroundColor">#FF252526</Color>
    <Color x:Key="SurfaceColor">#FF2D2D30</Color>
    <Color x:Key="CardColor">#FF3E3E42</Color>
    
    <!-- 文本颜色 -->
    <Color x:Key="TextPrimaryColor">#FFFFFFFF</Color>
    <Color x:Key="TextSecondaryColor">#FFCCCCCC</Color>
    <Color x:Key="TextDisabledColor">#FF808080</Color>
    
    <!-- 边框颜色 -->
    <Color x:Key="BorderColor">#FF464647</Color>
    <Color x:Key="BorderLightColor">#FF5A5A5C</Color>
    <Color x:Key="BorderDarkColor">#FF2D2D30</Color>
    
    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="{StaticResource BorderLightColor}"/>
    <SolidColorBrush x:Key="BorderDarkBrush" Color="{StaticResource BorderDarkColor}"/>
    
</ResourceDictionary>
