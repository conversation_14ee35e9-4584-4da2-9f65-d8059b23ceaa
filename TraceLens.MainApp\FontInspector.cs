using System;
using System.IO;
using System.Windows.Media;
using System.Diagnostics;

namespace TraceLens.MainApp
{
    public static class FontInspector
    {
        public static void InspectFontAwesome()
        {
            Debug.WriteLine("=== FontAwesome 字体检查 ===");

            // 检查字体文件路径 - FontAwesome 4
            string[] fontPaths = {
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf",
                // 尝试其他可能的字体族名称
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#fontawesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#Font Awesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome Regular"
            };

            foreach (string fontPath in fontPaths)
            {
                try
                {
                    var fontFamily = new FontFamily(fontPath);
                    Debug.WriteLine($"✓ 成功: {fontPath}");
                    Debug.WriteLine($"  Source: {fontFamily.Source}");
                    
                    // 尝试获取字体族名称
                    var typefaces = fontFamily.GetTypefaces();
                    foreach (var typeface in typefaces)
                    {
                        var familyNames = typeface.FontFamily.FamilyNames;
                        foreach (var name in familyNames)
                        {
                            Debug.WriteLine($"  Family Name: {name.Key} = {name.Value}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"✗ 失败: {fontPath}");
                    Debug.WriteLine($"  错误: {ex.Message}");
                }
                Debug.WriteLine("");
            }

            // 测试图标字符
            Debug.WriteLine("=== 图标字符测试 ===");
            string[] testChars = { "\uf015", "\uf007", "\uf013", "\uf030" }; // home, user, settings, camera
            string[] testNames = { "home", "user", "settings", "camera" };
            
            for (int i = 0; i < testChars.Length; i++)
            {
                Debug.WriteLine($"{testNames[i]}: '{testChars[i]}' (Unicode: U+{((int)testChars[i][0]):X4})");
            }
        }
    }
}
