using System;
using System.Windows;
using System.Windows.Media;
using TraceLens.ThemeControl.Controls;

namespace TraceLens.MainApp.Views
{
    public partial class HIconTestWindow : Window
    {
        public HIconTestWindow()
        {
            InitializeComponent();
            Loaded += HIconTestWindow_Loaded;
        }

        private void HIconTestWindow_Loaded(object sender, RoutedEventArgs e)
        {
            CreateManualFontIcons();
            ShowDebugInfo();
        }

        private void CreateManualFontIcons()
        {
            // 清空面板
            ManualFontPanel.Children.Clear();

            // 测试不同的字体路径
            string[] fontPaths = {
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf#FontAwesome",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/fontawesome-webfont.ttf",
                "pack://application:,,,/TraceLens.ThemeControl;component/Assets/fonts/FontAwesome.otf"
            };

            string[] iconNames = { "home", "user", "settings", "camera" };

            foreach (string fontPath in fontPaths)
            {
                try
                {
                    var fontFamily = new FontFamily(fontPath);
                    
                    foreach (string iconName in iconNames)
                    {
                        var icon = new HIcon
                        {
                            IconName = iconName,
                            IconSize = 24,
                            Margin = new Thickness(5),
                            FontFamily = fontFamily // 直接设置字体
                        };
                        
                        ManualFontPanel.Children.Add(icon);
                        
                        System.Diagnostics.Debug.WriteLine($"Created HIcon with FontFamily: {fontPath}, IconName: {iconName}");
                    }
                    
                    // 如果成功创建了图标，就停止尝试其他字体路径
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to create FontFamily: {fontPath} - {ex.Message}");
                }
            }
        }

        private void ShowDebugInfo()
        {
            var info = new System.Text.StringBuilder();
            
            // 检查第一个HIcon的属性
            if (ManualFontPanel.Children.Count > 0 && ManualFontPanel.Children[0] is HIcon firstIcon)
            {
                info.AppendLine($"第一个手动HIcon:");
                info.AppendLine($"  FontFamily: {firstIcon.FontFamily?.Source ?? "null"}");
                info.AppendLine($"  FontSize: {firstIcon.FontSize}");
                info.AppendLine($"  Text: '{firstIcon.Text}'");
                info.AppendLine($"  IconName: '{firstIcon.IconName}'");
                info.AppendLine($"  IconCode: '{firstIcon.IconCode}'");
                info.AppendLine();
            }
            
            // 检查FontAwesome管理器
            info.AppendLine("FontAwesome管理器测试:");
            string[] testIcons = { "home", "user", "settings", "camera" };
            foreach (string iconName in testIcons)
            {
                string iconCode = TraceLens.ThemeControl.Helpers.FontAwesomeManager.GetIconCode(iconName);
                info.AppendLine($"  {iconName} -> '{iconCode}' (Unicode: {(iconCode.Length > 0 ? ((int)iconCode[0]).ToString("X4") : "Empty")})");
            }
            
            info.AppendLine();
            
            // 检查资源
            try
            {
                var fontResource = this.FindResource("FontAwesome") as FontFamily;
                if (fontResource != null)
                {
                    info.AppendLine($"FontAwesome资源: {fontResource.Source}");
                }
                else
                {
                    info.AppendLine("FontAwesome资源: 未找到");
                }
            }
            catch (Exception ex)
            {
                info.AppendLine($"FontAwesome资源错误: {ex.Message}");
            }
            
            DebugInfo.Text = info.ToString();
        }
    }
}
