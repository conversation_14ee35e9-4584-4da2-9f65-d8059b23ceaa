<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- FontAwesome 图标字符定义 -->
    <!-- 常用图标 -->
    <system:String x:Key="Icon.Home" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf015;</system:String>
    <system:String x:Key="Icon.Settings" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf013;</system:String>
    <system:String x:Key="Icon.User" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf007;</system:String>
    <system:String x:Key="Icon.Search" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf002;</system:String>
    <system:String x:Key="Icon.Menu" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf0c9;</system:String>
    <system:String x:Key="Icon.Close" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf00d;</system:String>
    
    <!-- 工业相关图标 -->
    <system:String x:Key="Icon.Camera" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf030;</system:String>
    <system:String x:Key="Icon.Cog" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf013;</system:String>
    <system:String x:Key="Icon.Industry" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf275;</system:String>
    <system:String x:Key="Icon.Wrench" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf0ad;</system:String>
    <system:String x:Key="Icon.Gauge" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf0e4;</system:String>
    <system:String x:Key="Icon.Microchip" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf2db;</system:String>
    
    <!-- 操作图标 -->
    <system:String x:Key="Icon.Play" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf04b;</system:String>
    <system:String x:Key="Icon.Pause" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf04c;</system:String>
    <system:String x:Key="Icon.Stop" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf04d;</system:String>
    <system:String x:Key="Icon.Record" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf111;</system:String>
    <system:String x:Key="Icon.Save" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf0c7;</system:String>
    <system:String x:Key="Icon.Load" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf07c;</system:String>
    
    <!-- 状态图标 -->
    <system:String x:Key="Icon.Success" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf00c;</system:String>
    <system:String x:Key="Icon.Warning" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf071;</system:String>
    <system:String x:Key="Icon.Error" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf00d;</system:String>
    <system:String x:Key="Icon.Info" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf05a;</system:String>
    
    <!-- 导航图标 -->
    <system:String x:Key="Icon.ArrowLeft" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf060;</system:String>
    <system:String x:Key="Icon.ArrowRight" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf061;</system:String>
    <system:String x:Key="Icon.ArrowUp" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf062;</system:String>
    <system:String x:Key="Icon.ArrowDown" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf063;</system:String>
    
    <!-- 数据图标 -->
    <system:String x:Key="Icon.Chart" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf080;</system:String>
    <system:String x:Key="Icon.Table" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf0ce;</system:String>
    <system:String x:Key="Icon.List" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf03a;</system:String>
    <system:String x:Key="Icon.Grid" xmlns:system="clr-namespace:System;assembly=mscorlib">&#xf009;</system:String>
    
</ResourceDictionary>
