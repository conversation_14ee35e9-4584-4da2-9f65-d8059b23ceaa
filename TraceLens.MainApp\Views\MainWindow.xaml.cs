using System.Windows;

namespace TraceLens.MainApp.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // 检查字体
            FontInspector.InspectFontAwesome();
        }

        private void ShowIconDemo_Click(object sender, RoutedEventArgs e)
        {
            var iconDemoWindow = new IconDemoWindow();
            iconDemoWindow.Show();
        }

        private void ShowFontTest_Click(object sender, RoutedEventArgs e)
        {
            var fontTestWindow = new FontTestWindow();
            fontTestWindow.Show();
        }

        private void ShowHIconTest_Click(object sender, RoutedEventArgs e)
        {
            var hIconTestWindow = new HIconTestWindow();
            hIconTestWindow.Show();
        }
    }
}
