<Window x:Class="TraceLens.MainApp.Views.IconDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        Title="HIcon 使用演示" Height="700" Width="1000"
        Style="{StaticResource IndustrialWindow}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="15">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <controls:HIcon IconName="industry" IconSize="24" IconColor="{StaticResource AccentBrush}" Margin="0,0,10,0"/>
                <TextBlock Text="HIcon 控件使用演示" Style="{StaticResource TitleText}"/>
            </StackPanel>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                
                <!-- 基本使用方法 -->
                <GroupBox Header="基本使用方法" Foreground="{StaticResource TextPrimaryBrush}" Margin="0,0,0,20">
                    <Grid Margin="15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 使用图标名称 -->
                        <StackPanel Grid.Column="0" Margin="10">
                            <TextBlock Text="使用图标名称" Style="{StaticResource SubtitleText}" Margin="0,0,0,10"/>
                            <Border Background="{StaticResource SurfaceBrush}" Padding="20" CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:HIcon IconName="camera" IconSize="32" Margin="0,0,0,10"/>
                                    <TextBlock Text='IconName="camera"' FontFamily="Consolas" FontSize="10" 
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                        
                        <!-- 使用fa-前缀 -->
                        <StackPanel Grid.Column="1" Margin="10">
                            <TextBlock Text="使用fa-前缀" Style="{StaticResource SubtitleText}" Margin="0,0,0,10"/>
                            <Border Background="{StaticResource SurfaceBrush}" Padding="20" CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:HIcon IconName="fa-home" IconSize="32" Margin="0,0,0,10"/>
                                    <TextBlock Text='IconName="fa-home"' FontFamily="Consolas" FontSize="10" 
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                        
                        <!-- 直接使用Unicode -->
                        <StackPanel Grid.Column="2" Margin="10">
                            <TextBlock Text="直接使用Unicode" Style="{StaticResource SubtitleText}" Margin="0,0,0,10"/>
                            <Border Background="{StaticResource SurfaceBrush}" Padding="20" CornerRadius="4">
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:HIcon IconCode="&#xf013;" IconSize="32" Margin="0,0,0,10"/>
                                    <TextBlock Text='IconCode="&amp;#xf013;"' FontFamily="Consolas" FontSize="10" 
                                               HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </GroupBox>
                
                <!-- 不同大小演示 -->
                <GroupBox Header="不同大小演示" Foreground="{StaticResource TextPrimaryBrush}" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="15" HorizontalAlignment="Center">
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="小图标 (12px)" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="home" IconSize="12"/>
                        </StackPanel>

                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="默认 (16px)" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="home" IconSize="16"/>
                        </StackPanel>

                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="中等 (24px)" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="home" IconSize="24"/>
                        </StackPanel>

                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="大图标 (32px)" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="home" IconSize="32"/>
                        </StackPanel>

                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="超大 (48px)" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="home" IconSize="48"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- 状态颜色演示 -->
                <GroupBox Header="状态颜色演示" Foreground="{StaticResource TextPrimaryBrush}" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal" Margin="15" HorizontalAlignment="Center">
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="默认颜色" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="info" IconSize="24"/>
                        </StackPanel>
                        
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="强调色" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="info" IconSize="24" Style="{StaticResource AccentIcon}"/>
                        </StackPanel>
                        
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="成功" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="check" IconSize="24" Style="{StaticResource SuccessIcon}"/>
                        </StackPanel>
                        
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="警告" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="warning" IconSize="24" Style="{StaticResource WarningIcon}"/>
                        </StackPanel>
                        
                        <StackPanel Margin="20" HorizontalAlignment="Center">
                            <TextBlock Text="错误" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <controls:HIcon IconName="times" IconSize="24" Style="{StaticResource ErrorIcon}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
                
                <!-- 在按钮中使用 -->
                <GroupBox Header="在按钮中使用" Foreground="{StaticResource TextPrimaryBrush}" Margin="0,0,0,20">
                    <WrapPanel Margin="15" HorizontalAlignment="Center">
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="play" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="播放"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="pause" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="暂停"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="stop" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="停止"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="save" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="保存"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="load" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="加载"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource IndustrialButton}" Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <controls:HIcon IconName="settings" IconSize="16" Margin="0,0,5,0"/>
                                <TextBlock Text="设置"/>
                            </StackPanel>
                        </Button>
                    </WrapPanel>
                </GroupBox>
                
                <!-- 工业图标展示 -->
                <GroupBox Header="工业图标展示" Foreground="{StaticResource TextPrimaryBrush}" Margin="0,0,0,20">
                    <WrapPanel Margin="15" HorizontalAlignment="Center">
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="camera" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="camera" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="industry" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="industry" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="wrench" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="wrench" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="gauge" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="gauge" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="microchip" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="microchip" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <StackPanel Margin="15" HorizontalAlignment="Center">
                            <controls:HIcon IconName="cog" IconSize="24" Margin="0,0,0,5"/>
                            <TextBlock Text="cog" FontSize="10" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </WrapPanel>
                </GroupBox>
                
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
