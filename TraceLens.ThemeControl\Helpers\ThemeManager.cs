using System;
using System.Windows;

namespace TraceLens.ThemeControl.Helpers
{
    public static class ThemeManager
    {
        private static ResourceDictionary? _currentTheme;
        
        public static event EventHandler? ThemeChanged;
        
        /// <summary>
        /// 应用工业主题
        /// </summary>
        public static void ApplyIndustrialTheme()
        {
            ApplyTheme("pack://application:,,,/TraceLens.ThemeControl;component/Themes/Industrial/Theme.xaml");
        }
        
        /// <summary>
        /// 应用指定主题
        /// </summary>
        /// <param name="themeUri">主题资源URI</param>
        public static void ApplyTheme(string themeUri)
        {
            try
            {
                var newTheme = new ResourceDictionary
                {
                    Source = new Uri(themeUri, UriKind.Absolute)
                };
                
                ApplyTheme(newTheme);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to load theme from {themeUri}", ex);
            }
        }
        
        /// <summary>
        /// 应用主题资源字典
        /// </summary>
        /// <param name="theme">主题资源字典</param>
        public static void ApplyTheme(ResourceDictionary theme)
        {
            if (Application.Current?.Resources == null)
                throw new InvalidOperationException("Application resources not available");
            
            // 移除当前主题
            if (_currentTheme != null)
            {
                Application.Current.Resources.MergedDictionaries.Remove(_currentTheme);
            }
            
            // 应用新主题
            Application.Current.Resources.MergedDictionaries.Add(theme);
            _currentTheme = theme;
            
            // 触发主题变更事件
            ThemeChanged?.Invoke(null, EventArgs.Empty);
        }
        
        /// <summary>
        /// 获取当前主题资源
        /// </summary>
        /// <param name="key">资源键</param>
        /// <returns>资源对象</returns>
        public static object? GetThemeResource(string key)
        {
            return Application.Current?.Resources[key];
        }
        
        /// <summary>
        /// 获取当前主题资源
        /// </summary>
        /// <typeparam name="T">资源类型</typeparam>
        /// <param name="key">资源键</param>
        /// <returns>资源对象</returns>
        public static T? GetThemeResource<T>(string key) where T : class
        {
            return GetThemeResource(key) as T;
        }
    }
}
