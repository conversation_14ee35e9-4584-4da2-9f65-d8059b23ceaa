using Prism.Mvvm;
using Prism.Commands;
using TraceLens.MainApp.Services;
using System.Windows.Input;

namespace TraceLens.MainApp.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        private string _title = "TraceLens - Smart Vision Inspector";
        public string Title
        {
            get { return _title; }
            set { SetProperty(ref _title, value); }
        }

        private string _welcomeMessage = "Welcome to TraceLens Smart Vision Inspector";
        public string WelcomeMessage
        {
            get { return _welcomeMessage; }
            set { SetProperty(ref _welcomeMessage, value); }
        }

        private string _statusMessage = "Ready";
        public string StatusMessage
        {
            get { return _statusMessage; }
            set { SetProperty(ref _statusMessage, value); }
        }

        private readonly IInspectionService _inspectionService;

        public ICommand StartInspectionCommand { get; private set; }

        public MainWindowViewModel(IInspectionService inspectionService)
        {
            _inspectionService = inspectionService;

            // Initialize commands
            StartInspectionCommand = new DelegateCommand(ExecuteStartInspection);
        }

        private async void ExecuteStartInspection()
        {
            StatusMessage = "Starting inspection...";

            // TODO: Add file dialog to select image
            // For now, just simulate an inspection
            var result = await _inspectionService.InspectImageAsync("sample.jpg");

            StatusMessage = $"Inspection completed. Confidence: {result.ConfidenceScore:P}";
        }
    }
}
